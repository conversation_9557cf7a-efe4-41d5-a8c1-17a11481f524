import '@/styles/globals.css';

import type { Metadata, Viewport } from 'next';
import { ThemeProvider } from 'next-themes';
import type { CSSProperties, ReactNode } from 'react';

import { MainLayout } from '@/components/layout/main-layout';
import { inter } from '@/fonts';

export const viewport: Viewport = {
  themeColor: '#000000',
};

export const metadata: Metadata = {
  title: 'Your App Name',
  description: 'Your app description',
  metadataBase: new URL('https://google.com'),
  openGraph: {
    title: 'Your App Name',
    description: 'Your app description',
    url: 'https://google.com',
    siteName: 'Your App Name',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@username',
    creator: '@username',
  },
};

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  return (
    <html
      suppressHydrationWarning
      lang="en"
      style={
        {
          '--font-inter': inter.style.fontFamily,
        } as CSSProperties
      }
    >
      <body>
        {/* // TODO: Implement dark theme later */}
        <ThemeProvider forcedTheme="light">
          <MainLayout>{children}</MainLayout>
        </ThemeProvider>
      </body>
    </html>
  );
};

export default RootLayout;
