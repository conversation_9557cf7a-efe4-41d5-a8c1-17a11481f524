import type { ComponentPropsWithoutRef } from 'react';

import { BackgroundPattern } from '@/components/background-pattern';
import { Header } from '@/components/header';

type MainLayoutProps = ComponentPropsWithoutRef<'div'>;

export const MainLayout = ({ children }: MainLayoutProps) => {
  return (
    <BackgroundPattern>
      <div className="container mx-auto max-w-[720px]">
        <Header>{children}</Header>
      </div>
    </BackgroundPattern>
  );
};
