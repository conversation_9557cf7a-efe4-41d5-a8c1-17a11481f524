name: ci

# Cancel in-progress jobs if a new job is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

on:
  pull_request:

jobs:
  test_lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install
        uses: ./.github/actions/install
      - name: Run 'bun run lint` and `bun run format:fix` if this fails
        run: |
          bun run lint
          bun run format:check

  test:
    runs-on: ubuntu-latest
    needs: test_lint
    steps:
      - uses: actions/checkout@v4
      - name: Install
        uses: ./.github/actions/install
      - run: bun run build
      - run: bun run test
