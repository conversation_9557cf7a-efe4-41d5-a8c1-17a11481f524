<p align="center">
    <img src="https://user-images.githubusercontent.com/6702424/80216211-00ef5280-863e-11ea-81de-59f3a3d4b8e4.png">  
</p>
<p align="center">
    <i>This is my new personal website portfolio</i>
    <br>
    <br>
    <a href="https://github.com/ayungavis/personal/actions">
      <img src="https://github.com/ayungavis/personal/actions/workflows/ci.yaml/badge.svg?branch=main">
    </a>
    <a href="https://bundlephobia.com/package/personal">
      <img src="https://img.shields.io/bundlephobia/minzip/personal">
    </a>
    <a href="https://www.npmjs.com/package/personal">
      <img src="https://img.shields.io/npm/dw/personal">
    </a>
    <a href="https://github.com/ayungavis/personal/blob/main/LICENSE">
      <img src="https://img.shields.io/npm/l/personal">
    </a>
</p>

## Getting Started

To get started, follow these steps:

1. Clone the repository and install the dependencies:

```bash
git clone https://github.com/ayungavis/personal.git
cd personal
bun install
```

2. Run the development server:

```bash
bun dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Building the Application

To build the application, run:

```bash
bun build
```

This command generates an optimized production build in the `dist` directory.

### Running Tests

To run the tests, use the following command:

```bash
bun test
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
